# Loop Node Product Requirements Document (PRD)

## 🎯 **Product Overview**

The Loop Node is a control flow component in the orchestration engine that enables iterative execution of workflow transitions. It supports both simple single-transition loops and complex multi-transition chains, allowing workflows to process collections of data through sophisticated processing pipelines.

## 🔍 **Problem Statement**

Currently, the orchestration engine has basic loop functionality, but it lacks support for complex loop body chains where multiple transitions need to execute sequentially within each iteration. Users need the ability to create workflows like:

- **Simple Loop**: `loop -> process -> loop -> exit`
- **Complex Chain**: `loop -> step1 -> step2 -> step3 -> step4 -> loop -> exit`

The current implementation executes loop body and exit transitions in parallel, which breaks the intended sequential flow and produces incorrect results.

## 👥 **Target Users**

- **Workflow Designers**: Creating complex data processing workflows
- **Data Engineers**: Building ETL pipelines with iterative processing
- **Business Process Automators**: Implementing repetitive business logic
- **Integration Developers**: Connecting multiple systems through iterative operations

## 🎯 **Core Requirements**

### **1. Loop Configuration**

The loop node must support comprehensive configuration through six key elements:

#### **Iteration Behavior**

Controls how iterations relate to each other:

- **Independent**: Iterations can run in parallel (default)
- **Sequential**: Each iteration uses results from previous iterations

#### **Iteration Source**

Flexible sources for iteration data:

- **Lists**: Arrays of mixed-type items (strings, numbers, objects, booleans)
- **Number Ranges**: Start/end with optional step size
- **Batch Processing**: Process items in configurable batch sizes

#### **Exit Condition**

Conditions that determine when to exit the loop:

- **All Items Processed**: Complete when all iteration items are done (default)
- **Max Iterations**: Stop after a maximum number of iterations
- **Timeout**: Stop after a specified time limit
- **Success Condition**: Stop after achieving a number of successful iterations
- **Failure Threshold**: Stop after consecutive failures

#### **Iteration Settings**

Controls how iterations are executed:

- **Parallel Execution**: Enable/disable parallel iteration processing
- **Max Concurrent**: Maximum concurrent iterations (when parallel enabled)
- **Preserve Order**: Whether to maintain iteration order in results
- **Iteration Timeout**: Per-iteration timeout in seconds

#### **Result Aggregation**

How to combine results from all iterations:

- **Collect All**: Gather all iteration results
- **Collect Successful**: Only include successful iteration results
- **Count Only**: Return only the count of iterations
- **Latest Only**: Return only the most recent result
- **First Success**: Return the first successful result
- **Combine Text**: Concatenate text results

#### **Error Handling**

How to handle iteration failures:

- **Continue**: Skip failed iterations and continue
- **Retry Once/Twice**: Retry failed iterations
- **Exit Loop**: Stop the entire loop on first failure
- **Include Errors**: Whether to include error details in final results

```json
{
  "iteration_behavior": "independent",
  "iteration_source": {
    "iteration_list": [1, 2, 3, 4, 5],
    "batch_size": 2
  },
  "exit_condition": {
    "condition_type": "all_items_processed"
  },
  "iteration_settings": {
    "parallel_execution": false,
    "preserve_order": true,
    "iteration_timeout": 30
  },
  "result_aggregation": {
    "aggregation_type": "collect_all",
    "include_metadata": true
  },
  "error_handling": {
    "on_iteration_error": "continue",
    "include_errors": false
  }
}
```

### **2. Loop Body Execution**

The loop node supports loop body execution through transition chains. This unified approach handles both simple single-transition loops and complex multi-transition chains:

- **Single Transition**: `loop -> process -> loop -> exit`
- **Multi-Transition Chains**: `loop -> step1 -> step2 -> step3 -> step4 -> loop -> exit`
- Each iteration flows through the connected transitions
- Results collected from the final transition in the chain
- Chain execution managed by the orchestration engine

### **3. Connection Types**

The loop node has two distinct output types:

#### **Iteration Output**

- Provides current iteration item and context
- Connected to loop body transitions (entry points)
- Triggers for each iteration
- Contains: `current_item`, `iteration_index`, `iteration_context`

#### **Exit Output**

- Provides aggregated results from all iterations
- Connected to final workflow transitions
- Triggers once after all iterations complete
- Contains: `final_results`, `iteration_metadata`, `execution_summary`

### **4. Result Aggregation**

The loop node must aggregate results from loop body execution:

- **Collect All Results**: Gather results from every iteration
- **Collect Successful Only**: Filter out failed iterations

### **5. Execution Flow**

The correct execution sequence must be:

1. **Initialize Loop**: Parse iteration source and prepare data
2. **For Each Iteration**:
   - Send iteration data to loop body entry transitions
   - Wait for loop body chain to complete
   - Collect result from chain exit transitions
   - Store result for aggregation
3. **Complete Loop**: Aggregate all results and send to exit transitions

**Critical**: Loop body transitions and exit transitions must NOT execute in parallel.

## 🏗️ **Technical Architecture**

### **1. Loop Executor**

The main component responsible for:

- Parsing loop configuration
- Managing iteration state
- Coordinating with loop body chain executor
- Aggregating results
- Triggering exit transitions

### **2. Loop Body Chain Executor**

A unified component for handling all loop body execution (both simple and complex):

- Detecting chain entry and exit points
- Monitoring chain execution progress (single transitions or multi-transition chains)
- Collecting results from chain endpoints
- Managing chain state and timeouts

### **3. Transition Handler Integration**

Enhanced transition handler that:

- Recognizes loop body vs exit transitions
- Filters out loop body transitions from parallel execution
- Coordinates with loop executor for proper sequencing
- Handles chain completion notifications

### **4. Schema Enhancements**

Extended transition schema supporting:

- Loop body configuration specifications
- Handle type classifications (iteration, exit, chain entry/exit)
- Chain completion detection strategies
- Result collection configurations

## 📊 **Success Metrics**

### **Functional Success**

- ✅ Support for complex loop body chains
- ✅ Correct sequential execution (no parallel loop body/exit execution)
- ✅ Proper result aggregation from chain endpoints
- ✅ Backward compatibility with existing simple loops

### **Performance Success**

- Loop execution overhead < 10% compared to direct transition execution
- Support for parallel iteration processing when configured
- Efficient memory usage for large iteration datasets
- Chain execution timeout and error recovery

### **User Success**

- Workflow designers can create complex iterative processes
- Clear documentation and examples for loop configuration
- Intuitive connection patterns for loop body chains
- Reliable execution with predictable results

## 🚀 **Implementation Phases**

### **Phase 1: Foundation** (Week 1-2)

- Enhance transition schema for loop body chains
- Create loop body chain executor component
- Integrate with existing loop executor
- Basic chain detection and execution

### **Phase 2: Integration** (Week 3-4)

- Orchestration engine integration
- Transition completion notification system
- Chain state management and monitoring
- Error handling and timeout management

### **Phase 3: Advanced Features** (Week 5-6)

- Parallel iteration processing
- Advanced aggregation strategies
- Performance optimization
- Comprehensive testing and validation

### **Phase 4: Production Readiness** (Week 7-8)

- Documentation and examples
- Migration guide for existing workflows
- Performance benchmarking
- Production deployment and monitoring

## 🔧 **Configuration Examples**

### **Simple Loop with Basic Configuration**

```json
{
  "execution_type": "loop",
  "loop_config": {
    "iteration_behavior": "independent",
    "iteration_source": {
      "iteration_list": ["apple", "banana", "cherry"]
    },
    "exit_condition": {
      "condition_type": "all_items_processed"
    },
    "result_aggregation": {
      "aggregation_type": "collect_all"
    },
    "error_handling": {
      "on_iteration_error": "continue"
    }
  }
}
```

### **Complex Chain Loop with Full Configuration**

```json
{
  "execution_type": "loop",
  "loop_config": {
    "iteration_behavior": "sequential",
    "iteration_source": {
      "number_range": {
        "start": 1,
        "end": 100
      },
      "step": 5
    },
    "exit_condition": {
      "condition_type": "max_iterations",
      "max_iterations": 50
    },
    "iteration_settings": {
      "parallel_execution": false,
      "preserve_order": true,
      "iteration_timeout": 60
    },
    "loop_body_configuration": {
      "entry_transitions": ["validate-data"],
      "exit_transitions": ["save-result"],
      "chain_completion_detection": "explicit_exit_transitions"
    },
    "result_aggregation": {
      "aggregation_type": "collect_successful",
      "include_metadata": true
    },
    "error_handling": {
      "on_iteration_error": "retry_once",
      "include_errors": true
    }
  }
}
```

### **Batch Processing Example**

```json
{
  "execution_type": "loop",
  "loop_config": {
    "iteration_behavior": "independent",
    "iteration_source": {
      "iteration_list": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
      "batch_size": 3
    },
    "exit_condition": {
      "condition_type": "all_items_processed"
    },
    "iteration_settings": {
      "parallel_execution": true,
      "max_concurrent": 2,
      "preserve_order": true
    },
    "result_aggregation": {
      "aggregation_type": "collect_all",
      "include_metadata": false
    },
    "error_handling": {
      "on_iteration_error": "continue"
    }
  }
}
```

## 🎯 **Key Design Principles**

1. **Simplicity First**: Simple loops remain simple to configure
2. **Power When Needed**: Complex chains are possible but optional
3. **Backward Compatibility**: Existing workflows continue to work
4. **Clear Semantics**: Obvious distinction between iteration and exit outputs
5. **Reliable Execution**: Predictable, sequential execution flow
6. **Performance Conscious**: Efficient execution with optional parallelization
7. **Developer Friendly**: Clear APIs, good error messages, comprehensive logging

## 🔍 **Edge Cases & Considerations**

### **Error Handling**

- Failed iterations should not break the entire loop
- Configurable retry strategies for failed iterations
- Option to continue or abort on iteration failures
- Clear error reporting with iteration context

### **Performance**

- Large iteration datasets should not cause memory issues
- Optional streaming/lazy evaluation for huge datasets
- Configurable timeouts for chain execution
- Monitoring and metrics for loop performance

### **State Management**

- Loop state must be properly isolated between iterations
- Chain state must be cleaned up after completion
- Support for loop state persistence across system restarts
- Proper cleanup to prevent memory leaks

This PRD provides the foundation for implementing robust, flexible loop functionality that meets the complex requirements of modern workflow orchestration while maintaining simplicity for basic use cases.
