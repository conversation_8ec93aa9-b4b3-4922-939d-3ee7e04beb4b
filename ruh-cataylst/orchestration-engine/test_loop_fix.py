#!/usr/bin/env python3
"""
Test script to verify the loop execution fix.
This script tests that loop body transitions are executed properly during loop iterations.
"""

import asyncio
import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '.'))

from app.services.loop_executor.loop_executor import LoopExecutor
from app.core_.state_manager import WorkflowStateManager
from unittest.mock import Mock, AsyncMock


async def test_loop_execution_fix():
    """Test that the loop executor properly executes loop body transitions."""
    
    print("🧪 Testing loop execution fix...")
    
    # Create mock dependencies
    mock_state_manager = Mock()
    mock_state_manager.workflow_id = "test-workflow-123"
    mock_state_manager.store_loop_state = AsyncMock()
    mock_state_manager.save_workflow_state = AsyncMock()
    
    mock_workflow_utils = Mock()
    mock_result_callback = AsyncMock()
    
    # Create transitions_by_id with loop body transition
    transitions_by_id = {
        "transition-CombineTextComponent-1750769520925": {
            "id": "transition-CombineTextComponent-1750769520925",
            "node_id": "CombineTextComponent",
            "transition_type": "standard"
        }
    }
    
    nodes = {
        "CombineTextComponent": {
            "id": "CombineTextComponent",
            "type": "component"
        }
    }
    
    mock_transition_handler = Mock()
    
    # Create LoopExecutor instance
    loop_executor = LoopExecutor(
        state_manager=mock_state_manager,
        workflow_utils=mock_workflow_utils,
        result_callback=mock_result_callback,
        transitions_by_id=transitions_by_id,
        nodes=nodes,
        transition_handler=mock_transition_handler,
        user_id="test-user"
    )
    
    # Configure loop to iterate over [1, 3, 5, 7, 9] (same as the workflow)
    loop_config = {
        "loop_type": "context_independent",
        "aggregation_config": {"type": "collect_all"},
        "iteration_source": {
            "type": "range",
            "data": {
                "start": 1,
                "stop": 10,
                "step": 2
            }
        },
        "loop_body_transitions": ["transition-CombineTextComponent-1750769520925"]
    }
    
    print(f"📋 Loop config: {loop_config}")
    
    # Execute the loop
    try:
        result = await loop_executor.execute_tool(
            loop_config=loop_config,
            transition_id="test-loop-transition"
        )
        
        print(f"✅ Loop execution completed successfully!")
        print(f"📊 Result structure: {type(result)}")
        
        if isinstance(result, dict):
            print(f"🔍 Result keys: {list(result.keys())}")
            
            # Check iteration outputs
            if "iteration_outputs" in result:
                iteration_outputs = result["iteration_outputs"]
                print(f"🔄 Iteration outputs count: {len(iteration_outputs)}")
                for i, output in enumerate(iteration_outputs):
                    print(f"   Iteration {i}: {output}")
            
            # Check exit output (aggregated results)
            if "exit_output" in result:
                exit_output = result["exit_output"]
                print(f"🚪 Exit output: {exit_output}")
                
                if "aggregated_results" in exit_output:
                    aggregated = exit_output["aggregated_results"]
                    print(f"📈 Aggregated results count: {len(aggregated)}")
                    for i, agg_result in enumerate(aggregated):
                        print(f"   Aggregated {i}: {agg_result}")
                        
                    # Verify that the results show the loop body was executed
                    # Each result should be like "1coming", "3coming", etc.
                    expected_results = ["1coming", "3coming", "5coming", "7coming", "9coming"]
                    actual_results = [str(result) for result in aggregated]
                    
                    print(f"🎯 Expected results: {expected_results}")
                    print(f"🎯 Actual results: {actual_results}")
                    
                    if actual_results == expected_results:
                        print("✅ SUCCESS: Loop body transitions were executed correctly!")
                        print("   Each iteration item was combined with 'coming' as expected.")
                        return True
                    else:
                        print("❌ FAILURE: Loop body results don't match expected output.")
                        return False
        
        print("❌ FAILURE: Unexpected result structure.")
        return False
        
    except Exception as e:
        print(f"❌ FAILURE: Loop execution failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Main test function."""
    print("🚀 Starting loop execution fix test...")
    
    success = await test_loop_execution_fix()
    
    if success:
        print("\n🎉 All tests passed! The loop execution fix is working correctly.")
        print("   Loop body transitions are now executed during each iteration.")
        print("   The aggregated results contain the processed data from loop body executions.")
    else:
        print("\n💥 Tests failed! The loop execution fix needs more work.")
    
    return success


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
